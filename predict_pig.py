#!/usr/bin/env python3
"""
YOLO11 猪检测推理脚本 - 支持图片、视频、推流三种方式
使用训练好的模型对不同输入源进行猪检测
"""

import os
import argparse
import threading
import subprocess
import time
import signal
import sys
from pathlib import Path
from ultralytics import YOLO
import cv2
import numpy as np
from flask import Flask, send_file, jsonify, Response
import queue
import json
from datetime import datetime

class PigDetectionStreamer:
    def __init__(self, model_path, conf_threshold=0.25, stream_port=8080, hls_port=8081):
        """
        初始化检测流媒体器
        
        Args:
            model_path: YOLO模型路径
            conf_threshold: 置信度阈值
            stream_port: 流媒体服务端口
            hls_port: HLS服务端口
        """
        self.model = YOLO(model_path)
        self.conf_threshold = conf_threshold
        self.stream_port = stream_port
        self.hls_port = hls_port
        self.is_streaming = False
        self.frame_queue = queue.Queue(maxsize=10)
        self.detection_stats = {"total_frames": 0, "detected_pigs": 0, "last_detection_time": None}
        
        # HLS配置
        self.hls_dir = "hls_output"
        self.segment_time = 2  # 每个片段2秒
        self.playlist_size = 5  # 保持5个片段
        
        # 创建HLS输出目录
        os.makedirs(self.hls_dir, exist_ok=True)
        
        # Flask应用
        self.app = Flask(__name__)
        self.setup_routes()
        
    def setup_routes(self):
        """设置Flask路由"""
        @self.app.route('/live.m3u8')
        def serve_m3u8():
            """提供m3u8播放列表"""
            m3u8_path = os.path.join(self.hls_dir, "live.m3u8")
            if os.path.exists(m3u8_path):
                return send_file(m3u8_path, mimetype='application/vnd.apple.mpegurl')
            return "Stream not available", 404
        
        @self.app.route('/live<int:segment_num>.ts')
        def serve_segment(segment_num):
            """提供TS片段"""
            segment_path = os.path.join(self.hls_dir, f"live{segment_num}.ts")
            if os.path.exists(segment_path):
                return send_file(segment_path, mimetype='video/mp2t')
            return "Segment not found", 404
        
        @self.app.route('/stats')
        def get_stats():
            """获取检测统计信息"""
            return jsonify(self.detection_stats)
        
        @self.app.route('/stream_url')
        def get_stream_url():
            """获取流媒体URL"""
            # 在远程服务器环境中，使用相对路径更安全
            return jsonify({
                "hls_url": f"/live.m3u8",
                "hls_url_full": f"http://0.0.0.0:{self.hls_port}/live.m3u8",
                "status": "streaming" if self.is_streaming else "stopped",
                "port": self.hls_port
            })

        @self.app.route('/')
        def index():
            """提供简单的状态页面"""
            return f"""
            <!DOCTYPE html>
            <html>
            <head>
                <title>猪检测推流服务</title>
                <meta charset="utf-8">
                <meta name="viewport" content="width=device-width, initial-scale=1">
                <style>
                    body {{ font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }}
                    .container {{ max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }}
                    .status {{ padding: 10px; border-radius: 4px; margin: 10px 0; }}
                    .status.running {{ background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }}
                    .status.stopped {{ background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }}
                    .links {{ margin: 20px 0; }}
                    .links a {{ display: inline-block; margin: 5px 10px 5px 0; padding: 8px 16px; background-color: #007bff; color: white; text-decoration: none; border-radius: 4px; }}
                    .links a:hover {{ background-color: #0056b3; }}
                    .stats {{ margin: 20px 0; padding: 15px; background-color: #f8f9fa; border-radius: 4px; }}
                    .refresh {{ margin: 10px 0; }}
                    .refresh button {{ padding: 8px 16px; background-color: #28a745; color: white; border: none; border-radius: 4px; cursor: pointer; }}
                    .refresh button:hover {{ background-color: #218838; }}
                </style>
                <script>
                    function refreshStats() {{
                        fetch('/stats')
                            .then(response => response.json())
                            .then(data => {{
                                document.getElementById('total-frames').textContent = data.total_frames || 0;
                                document.getElementById('detected-pigs').textContent = data.detected_pigs || 0;
                                document.getElementById('last-detection').textContent = data.last_detection_time || '无';
                                const rate = data.total_frames > 0 ? ((data.detected_pigs / data.total_frames) * 100).toFixed(2) : 0;
                                document.getElementById('detection-rate').textContent = rate + '%';
                            }})
                            .catch(error => console.error('Error:', error));
                    }}

                    // 每5秒自动刷新统计信息
                    setInterval(refreshStats, 5000);

                    // 页面加载时刷新一次
                    window.onload = refreshStats;
                </script>
            </head>
            <body>
                <div class="container">
                    <h1>🐷 猪检测推流服务</h1>

                    <div class="status {'running' if self.is_streaming else 'stopped'}">
                        <strong>服务状态:</strong> {'🟢 运行中' if self.is_streaming else '🔴 已停止'}
                    </div>

                    <div class="stats">
                        <h3>📊 实时统计</h3>
                        <p><strong>总处理帧数:</strong> <span id="total-frames">-</span></p>
                        <p><strong>检测到的猪:</strong> <span id="detected-pigs">-</span></p>
                        <p><strong>检测率:</strong> <span id="detection-rate">-</span></p>
                        <p><strong>最后检测时间:</strong> <span id="last-detection">-</span></p>

                        <div class="refresh">
                            <button onclick="refreshStats()">🔄 刷新统计</button>
                        </div>
                    </div>

                    <div class="links">
                        <h3>🔗 服务链接</h3>
                        <a href="/live.m3u8" target="_blank">📺 HLS直播流</a>
                        <a href="/stats" target="_blank">📈 统计API</a>
                        <a href="/stream_url" target="_blank">🔗 流URL信息</a>
                    </div>

                    <div style="margin-top: 30px; padding: 15px; background-color: #e9ecef; border-radius: 4px;">
                        <h4>💡 使用说明</h4>
                        <ul>
                            <li>点击"HLS直播流"可以下载播放列表文件</li>
                            <li>使用支持HLS的播放器（如VLC）播放直播流</li>
                            <li>统计信息每5秒自动更新</li>
                            <li>检测率 = 检测到猪的帧数 / 总处理帧数</li>
                        </ul>
                    </div>
                </div>
            </body>
            </html>
            """

def draw_boxes_only(image, boxes, thickness=1):
    """
    只绘制边界框，不绘制标签和置信度
    
    Args:
        image: 原始图像
        boxes: 检测框
        thickness: 线条粗细
    """
    if boxes is None:
        return image
    
    # 复制图像以避免修改原图
    annotated_img = image.copy()
    
    # 绘制每个检测框
    for box in boxes:
        x1, y1, x2, y2 = box.xyxy[0].cpu().numpy().astype(int)
        # 只绘制边界框，使用较细的线条
        cv2.rectangle(annotated_img, (x1, y1), (x2, y2), (255, 255, 0), thickness=3)
    
    return annotated_img

def predict_image(model, image_path, conf_threshold=0.25, iou_threshold=0.45, 
                 max_det=300, save_dir="predictions"):
    """
    对单张图片进行预测
    
    Args:
        model: YOLO模型
        image_path: 图片路径
        conf_threshold: 置信度阈值
        iou_threshold: IOU阈值
        max_det: 最大检测数量
        save_dir: 保存目录
    """
    if not os.path.exists(image_path):
        print(f"错误: 图片文件 {image_path} 不存在!")
        return
    
    print(f"正在预测图片: {image_path}")
    
    # 进行预测
    results = model(image_path, conf=conf_threshold, iou=iou_threshold, max_det=max_det)
    
    # 保存结果
    os.makedirs(save_dir, exist_ok=True)
    
    for i, result in enumerate(results):
        # 读取原始图像
        original_img = cv2.imread(image_path)
        
        # 只绘制边界框，线条粗细设为1
        annotated_img = draw_boxes_only(original_img, result.boxes, thickness=1)
        
        output_path = os.path.join(save_dir, f"predicted_{os.path.basename(image_path)}")
        cv2.imwrite(output_path, annotated_img)
        print(f"预测结果已保存到: {output_path}")
        
        # 打印检测结果
        if result.boxes is not None:
            boxes = result.boxes
            print(f"检测到 {len(boxes)} 只猪:")
            for j, box in enumerate(boxes):
                conf = box.conf[0].item()
                x1, y1, x2, y2 = box.xyxy[0].tolist()
                print(f"  猪 {j+1}: 置信度={conf:.3f}, 坐标=({x1:.1f}, {y1:.1f}, {x2:.1f}, {y2:.1f})")
        else:
            print("未检测到猪")

def predict_video(model, video_path, conf_threshold=0.25, iou_threshold=0.45, 
                 max_det=300, save_dir="predictions"):
    """
    对视频进行预测
    
    Args:
        model: YOLO模型
        video_path: 视频路径
        conf_threshold: 置信度阈值
        iou_threshold: IOU阈值
        max_det: 最大检测数量
        save_dir: 保存目录
    """
    if not os.path.exists(video_path):
        print(f"错误: 视频文件 {video_path} 不存在!")
        return
    
    print(f"正在预测视频: {video_path}")
    
    # 打开视频
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        print(f"错误: 无法打开视频文件 {video_path}")
        return
    
    # 获取视频信息
    fps = int(cap.get(cv2.CAP_PROP_FPS))
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    
    print(f"视频信息: {width}x{height}, {fps}fps, {total_frames}帧")
    
    # 创建输出视频
    os.makedirs(save_dir, exist_ok=True)
    output_path = os.path.join(save_dir, f"predicted_{os.path.basename(video_path)}")
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
    
    frame_count = 0
    total_detections = 0
    
    while True:
        ret, frame = cap.read()
        if not ret:
            break
        
        frame_count += 1
        print(f"处理帧 {frame_count}/{total_frames}", end='\r')
        
        # 进行预测
        results = model(frame, conf=conf_threshold, iou=iou_threshold, max_det=max_det)
        
        # 统计检测结果
        if results[0].boxes is not None:
            total_detections += len(results[0].boxes)
        
        # 只绘制边界框，线条粗细设为1
        annotated_frame = draw_boxes_only(frame, results[0].boxes, thickness=1)
        
        # 写入输出视频
        out.write(annotated_frame)
    
    # 释放资源
    cap.release()
    out.release()
    print(f"\n预测完成! 结果已保存到: {output_path}")
    print(f"总检测数量: {total_detections}")

def start_streaming(model, source, conf_threshold=0.25, iou_threshold=0.45, 
                   max_det=300, stream_port=8080, hls_port=8081):
    """
    启动推流服务
    
    Args:
        model: YOLO模型
        source: 输入源 (摄像头ID/视频文件/RTMP流)
        conf_threshold: 置信度阈值
        iou_threshold: IOU阈值
        max_det: 最大检测数量
        stream_port: 流媒体服务端口
        hls_port: HLS服务端口
    """
    streamer = PigDetectionStreamer(model, conf_threshold, stream_port, hls_port)
    
    # 启动HLS服务器
    def run_hls_server():
        streamer.app.run(host='0.0.0.0', port=hls_port, debug=False, threaded=True)
    
    hls_thread = threading.Thread(target=run_hls_server)
    hls_thread.daemon = True
    hls_thread.start()
    
    print(f"HLS服务器已启动: http://0.0.0.0:{hls_port}")
    print(f"直播流地址: http://0.0.0.0:{hls_port}/live.m3u8")
    print(f"统计信息: http://0.0.0.0:{hls_port}/stats")
    print(f"状态页面: http://0.0.0.0:{hls_port}/")
    print("=" * 50)
    print("远程GPU服务器访问说明:")
    print("1. 确保在创建实例时配置了Web应用预览端口")
    print(f"2. 配置的端口应为: {hls_port}")
    print("3. 通过平台的'Web应用预览'功能访问服务")
    print("=" * 50)
    
    # 打开输入源
    print(f"正在连接视频源: {source}")

    # 处理不同类型的输入源
    cap = None
    max_retries = 3
    retry_count = 0

    while retry_count < max_retries and cap is None:
        try:
            # 尝试将source转换为整数（摄像头ID）
            if source.isdigit():
                cap = cv2.VideoCapture(int(source))
                print(f"尝试连接摄像头 {source}")
            else:
                # 对于WSS流或其他网络流，使用特殊的参数
                if source.startswith('wss://') or source.startswith('ws://'):
                    print(f"检测到WebSocket流，尝试使用ffmpeg处理: {source}")
                    # 使用ffmpeg来处理WSS流
                    ffmpeg_source = f"ffmpeg -i {source} -f rawvideo -pix_fmt bgr24 -"
                    cap = cv2.VideoCapture(ffmpeg_source, cv2.CAP_FFMPEG)
                else:
                    # 普通文件路径或RTMP流
                    cap = cv2.VideoCapture(source)
                    print(f"尝试连接流媒体源: {source}")

            # 检查是否成功打开
            if cap is not None and cap.isOpened():
                print("视频源连接成功!")
                break
            else:
                print(f"连接失败，重试 {retry_count + 1}/{max_retries}")
                if cap is not None:
                    cap.release()
                cap = None
                retry_count += 1
                time.sleep(2)  # 等待2秒后重试

        except Exception as e:
            print(f"连接视频源时出错: {e}")
            if cap is not None:
                cap.release()
            cap = None
            retry_count += 1
            time.sleep(2)

    if cap is None or not cap.isOpened():
        print(f"错误: 经过 {max_retries} 次尝试后仍无法打开输入源 {source}")
        print("请检查:")
        print("1. 网络连接是否正常")
        print("2. 视频流地址是否正确")
        print("3. 是否安装了ffmpeg（处理WSS流需要）")
        return
    
    # 获取视频信息
    fps = int(cap.get(cv2.CAP_PROP_FPS)) or 30
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    
    print(f"输入源信息: {width}x{height}, {fps}fps")
    
    # 启动FFmpeg进程用于HLS编码
    ffmpeg_cmd = [
        'ffmpeg',
        '-y',  # 覆盖输出文件
        '-f', 'rawvideo',
        '-vcodec', 'rawvideo',
        '-pix_fmt', 'bgr24',
        '-s', f'{width}x{height}',
        '-r', str(fps),
        '-i', '-',  # 从stdin读取
        '-c:v', 'libx264',
        '-preset', 'ultrafast',
        '-tune', 'zerolatency',
        '-crf', '23',
        '-g', str(fps),  # 关键帧间隔
        '-hls_time', str(streamer.segment_time),
        '-hls_list_size', str(streamer.playlist_size),
        '-hls_flags', 'delete_segments',
        '-f', 'hls',
        os.path.join(streamer.hls_dir, 'live.m3u8')
    ]
    
    try:
        ffmpeg_process = subprocess.Popen(ffmpeg_cmd, stdin=subprocess.PIPE, 
                                        stderr=subprocess.PIPE)
        
        streamer.is_streaming = True
        frame_count = 0
        
        print("开始推流...")
        
        consecutive_failures = 0
        max_consecutive_failures = 10

        while True:
            ret, frame = cap.read()
            if not ret:
                consecutive_failures += 1
                print(f"读取帧失败 ({consecutive_failures}/{max_consecutive_failures})")

                if consecutive_failures >= max_consecutive_failures:
                    print("连续读取失败次数过多，尝试重新连接...")
                    cap.release()

                    # 重新连接
                    time.sleep(5)  # 等待5秒
                    try:
                        if source.startswith('wss://') or source.startswith('ws://'):
                            ffmpeg_source = f"ffmpeg -i {source} -f rawvideo -pix_fmt bgr24 -"
                            cap = cv2.VideoCapture(ffmpeg_source, cv2.CAP_FFMPEG)
                        elif source.isdigit():
                            cap = cv2.VideoCapture(int(source))
                        else:
                            cap = cv2.VideoCapture(source)

                        if cap.isOpened():
                            print("重新连接成功!")
                            consecutive_failures = 0
                            continue
                        else:
                            print("重新连接失败，退出推流")
                            break
                    except Exception as e:
                        print(f"重新连接时出错: {e}")
                        break
                else:
                    time.sleep(0.1)  # 短暂等待后继续尝试
                    continue
            else:
                consecutive_failures = 0  # 重置失败计数器
            
            frame_count += 1
            streamer.detection_stats["total_frames"] = frame_count
            
            # 进行预测
            results = model(frame, conf=conf_threshold, iou=iou_threshold, max_det=max_det)
            
            # 统计检测结果
            if results[0].boxes is not None:
                pig_count = len(results[0].boxes)
                streamer.detection_stats["detected_pigs"] += pig_count
                if pig_count > 0:
                    streamer.detection_stats["last_detection_time"] = datetime.now().isoformat()
            
            # 只绘制边界框
            annotated_frame = draw_boxes_only(frame, results[0].boxes, thickness=1)
            
            # 发送到FFmpeg
            try:
                ffmpeg_process.stdin.write(annotated_frame.tobytes())
                ffmpeg_process.stdin.flush()
            except BrokenPipeError:
                print("FFmpeg进程已断开")
                break
            
            # 显示实时统计（每100帧）
            if frame_count % 100 == 0:
                print(f"已处理 {frame_count} 帧，检测到 {streamer.detection_stats['detected_pigs']} 只猪")
    
    except KeyboardInterrupt:
        print("\n正在停止推流...")
    
    finally:
        streamer.is_streaming = False
        cap.release()
        if ffmpeg_process:
            ffmpeg_process.stdin.close()
            ffmpeg_process.terminate()
            ffmpeg_process.wait()
        print("推流已停止")

def predict_folder(model, folder_path, conf_threshold=0.25, iou_threshold=0.45, 
                  max_det=300, save_dir="predictions"):
    """
    对文件夹中的所有图片进行预测
    
    Args:
        model: YOLO模型
        folder_path: 文件夹路径
        conf_threshold: 置信度阈值
        iou_threshold: IOU阈值
        max_det: 最大检测数量
        save_dir: 保存目录
    """
    if not os.path.exists(folder_path):
        print(f"错误: 文件夹 {folder_path} 不存在!")
        return
    
    # 支持的图片格式
    image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'}
    
    # 获取所有图片文件
    image_files = []
    for file in os.listdir(folder_path):
        if os.path.splitext(file)[1].lower() in image_extensions:
            image_files.append(os.path.join(folder_path, file))
    
    if not image_files:
        print(f"在文件夹 {folder_path} 中未找到图片文件")
        return
    
    print(f"找到 {len(image_files)} 张图片")
    
    # 批量预测
    for i, image_path in enumerate(image_files):
        print(f"处理图片 {i+1}/{len(image_files)}: {os.path.basename(image_path)}")
        predict_image(model, image_path, conf_threshold, iou_threshold, max_det, save_dir)

def main():
    parser = argparse.ArgumentParser(description='YOLO11 猪检测推理脚本 - 支持图片、视频、推流')
    parser.add_argument('--model', type=str, default='runs/detect/pig_detection/weights/best.pt',
                       help='模型路径')
    parser.add_argument('--source', type=str,
                       default='wss://cameracc.cdelinks.cn:4431/rtp/34020000001320000065_34020000001320000065.live.flv',
                       help='输入源 (图片路径/视频路径/文件夹路径/摄像头ID/RTMP流/WSS流)')
    parser.add_argument('--mode', type=str, choices=['image', 'video', 'stream'],
                       default='auto', help='运行模式: image(图片), video(视频), stream(推流)')
    
    # 推理参数
    parser.add_argument('--conf', type=float, default=0.25,
                       help='置信度阈值 (0.0-1.0)')
    parser.add_argument('--iou', type=float, default=0.45,
                       help='NMS IOU阈值 (0.0-1.0)')
    parser.add_argument('--max-det', type=int, default=300,
                       help='每张图片的最大检测数量')
    parser.add_argument('--imgsz', type=int, default=640,
                       help='推理图片大小')
    parser.add_argument('--device', type=str, default='',
                       help='设备 (cpu, 0, 1, 2, 3, auto)')
    parser.add_argument('--half', action='store_true',
                       help='使用FP16半精度推理')
    parser.add_argument('--augment', action='store_true',
                       help='增强推理')
    parser.add_argument('--agnostic-nms', action='store_true',
                       help='类别无关的NMS')
    
    # 输出参数
    parser.add_argument('--save-dir', type=str, default='predictions',
                       help='保存目录')
    parser.add_argument('--stream-port', type=int, default=8080,
                       help='流媒体服务端口')
    parser.add_argument('--hls-port', type=int, default=8081,
                       help='HLS服务端口')
    
    args = parser.parse_args()
    
    # 检查模型文件是否存在
    if not os.path.exists(args.model):
        print(f"错误: 模型文件 {args.model} 不存在!")
        print("请先训练模型或提供正确的模型路径")
        return
    
    # 加载模型
    print(f"加载模型: {args.model}")
    model = YOLO(args.model)
    
    # 设置模型参数
    if args.device:
        model.to(args.device)
    if args.half:
        model.half()
    
    # 自动判断模式
    if args.mode == 'auto':
        if os.path.isfile(args.source):
            file_ext = os.path.splitext(args.source)[1].lower()
            if file_ext in {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'}:
                args.mode = 'image'
            elif file_ext in {'.mp4', '.avi', '.mov', '.mkv', '.flv', '.wmv'}:
                args.mode = 'video'
            else:
                print(f"不支持的文件格式: {file_ext}")
                return
        elif os.path.isdir(args.source):
            args.mode = 'image'  # 文件夹模式
        else:
            # 可能是摄像头ID或流地址
            args.mode = 'stream'
    
    # 打印配置信息
    print(f"运行模式: {args.mode}")
    print(f"推理参数: conf={args.conf}, iou={args.iou}, max_det={args.max_det}")
    print(f"图片大小: {args.imgsz}, 设备: {args.device or 'auto'}")
    print(f"半精度: {args.half}, 增强推理: {args.augment}")
    
    # 根据模式执行不同的预测
    if args.mode == 'image':
        if os.path.isfile(args.source):
            predict_image(model, args.source, args.conf, args.iou, args.max_det, args.save_dir)
        elif os.path.isdir(args.source):
            predict_folder(model, args.source, args.conf, args.iou, args.max_det, args.save_dir)
        else:
            print(f"错误: 输入源 {args.source} 不存在!")
    
    elif args.mode == 'video':
        predict_video(model, args.source, args.conf, args.iou, args.max_det, args.save_dir)
    
    elif args.mode == 'stream':
        start_streaming(model, args.source, args.conf, args.iou, args.max_det, 
                       args.stream_port, args.hls_port)
    
    else:
        print(f"不支持的模式: {args.mode}")

if __name__ == "__main__":
    main()
