# 🚀 猪检测推流服务快速启动指南

## 📋 文件说明

本项目包含以下主要文件：

| 文件名 | 说明 |
|--------|------|
| `predict_pig.py` | 主要的推理脚本，支持图片、视频、推流 |
| `start_pig_stream.py` | 简化的启动脚本 |
| `test_stream_connection.py` | 视频流连接测试脚本 |
| `monitor_service.py` | 服务监控脚本 |
| `deploy_pig_detection.sh` | 一键部署脚本 |
| `README_REMOTE_STREAMING.md` | 详细部署文档 |

## ⚡ 快速启动（3步搞定）

### 步骤1: 一键部署
```bash
# 给脚本执行权限
chmod +x deploy_pig_detection.sh

# 运行一键部署
./deploy_pig_detection.sh
```

### 步骤2: 启动服务
```bash
# 方法1: 使用启动脚本（推荐）
python3 start_pig_stream.py --port 8081

# 方法2: 直接使用主脚本
python3 predict_pig.py --mode stream --hls-port 8081
```

### 步骤3: 访问服务
1. 在极值算力实例列表中点击"详情"
2. 找到"Web应用预览"并点击"查看"
3. 确保配置的端口为: 8081

## 🔧 手动部署（如果一键部署失败）

### 1. 安装依赖
```bash
# 系统依赖
sudo apt-get update
sudo apt-get install -y ffmpeg

# Python依赖
pip3 install ultralytics opencv-python flask numpy torch torchvision
```

### 2. 测试连接
```bash
python3 test_stream_connection.py
```

### 3. 启动服务
```bash
python3 start_pig_stream.py --port 8081 --conf 0.25
```

## 📊 监控服务

### 启动监控
```bash
# 基本监控
python3 monitor_service.py

# 自定义监控
python3 monitor_service.py --host localhost --port 8081 --interval 10
```

### 导出统计数据
```bash
python3 monitor_service.py --export stats_$(date +%Y%m%d).json
```

## 🌐 Web界面功能

访问服务后，您可以使用以下功能：

### 主页面 (`/`)
- 📊 实时统计信息（自动刷新）
- 🟢 服务状态显示
- 🔗 快速访问链接
- 💡 使用说明

### API端点
- `/live.m3u8` - HLS直播流播放列表
- `/stats` - JSON格式的统计信息
- `/stream_url` - 流URL信息

## 🎯 配置参数

### 启动脚本参数
```bash
python3 start_pig_stream.py [选项]

--model PATH     模型路径 (默认: runs/detect/pig_detection/weights/best.pt)
--port PORT      Web服务端口 (默认: 8081, 范围: 2000-65000)
--conf FLOAT     置信度阈值 (默认: 0.25)
--check-deps     只检查依赖，不启动服务
```

### 主脚本参数
```bash
python3 predict_pig.py [选项]

--mode stream           推流模式
--source URL           视频源地址
--hls-port PORT        HLS服务端口
--conf FLOAT           置信度阈值
--iou FLOAT            IOU阈值
--max-det INT          最大检测数量
```

## 🔍 故障排查

### 常见问题及解决方案

#### 1. 无法连接视频源
```
错误: 经过 3 次尝试后仍无法打开输入源
```
**解决方案:**
- 检查网络连接: `ping cameracc.cdelinks.cn`
- 确认FFmpeg安装: `ffmpeg -version`
- 测试连接: `python3 test_stream_connection.py`

#### 2. Web应用预览无法访问
**解决方案:**
- 确认实例创建时已配置Web应用预览端口
- 检查端口号是否与启动服务时使用的端口一致
- 确认服务已成功启动（查看终端输出）

#### 3. 推流中断
```
读取帧失败 (x/10)
```
**解决方案:**
- 服务会自动尝试重连
- 如果持续失败，检查网络稳定性
- 重启服务: `Ctrl+C` 然后重新启动

#### 4. 模型文件不存在
```
错误: 模型文件 xxx 不存在
```
**解决方案:**
- 检查模型路径是否正确
- 如果没有训练好的模型，系统会自动下载YOLO11x.pt预训练模型

## 📈 性能优化

### 1. 调整检测参数
```bash
# 降低置信度阈值（检测更多目标）
python3 start_pig_stream.py --conf 0.15

# 提高置信度阈值（减少误检）
python3 start_pig_stream.py --conf 0.35
```

### 2. 监控资源使用
```bash
# 查看GPU使用情况
nvidia-smi

# 查看CPU和内存使用
htop
```

### 3. 网络优化
- 确保网络带宽充足
- 使用有线网络而非WiFi
- 检查防火墙设置

## 📞 获取帮助

### 检查服务状态
```bash
# 查看服务是否运行
curl http://localhost:8081/

# 查看统计信息
curl http://localhost:8081/stats

# 查看流信息
curl http://localhost:8081/stream_url
```

### 查看日志
- 服务启动时的终端输出包含详细的状态信息
- 注意查看连接状态和错误信息
- 使用监控脚本获取实时状态

### 重启服务
```bash
# 停止服务
Ctrl+C

# 重新启动
python3 start_pig_stream.py --port 8081
```

## 🎉 成功标志

当您看到以下输出时，说明服务启动成功：

```
HLS服务器已启动: http://0.0.0.0:8081
直播流地址: http://0.0.0.0:8081/live.m3u8
统计信息: http://0.0.0.0:8081/stats
状态页面: http://0.0.0.0:8081/
==================================================
远程GPU服务器访问说明:
1. 确保在创建实例时配置了Web应用预览端口
2. 配置的端口应为: 8081
3. 通过平台的'Web应用预览'功能访问服务
==================================================
正在连接视频源: wss://cameracc.cdelinks.cn:4431/rtp/...
视频源连接成功!
开始推流...
```

现在您可以通过Web应用预览功能访问服务了！🎊
