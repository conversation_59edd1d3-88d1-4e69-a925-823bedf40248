#!/bin/bash

# 猪检测推流服务一键部署脚本
# 适用于远程GPU服务器环境

set -e  # 遇到错误立即退出

echo "=========================================="
echo "猪检测推流服务一键部署脚本"
echo "=========================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -eq 0 ]]; then
        print_warning "检测到root用户，建议使用普通用户运行"
    fi
}

# 检查系统环境
check_system() {
    print_info "检查系统环境..."
    
    # 检查操作系统
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        print_info "操作系统: Linux"
    else
        print_warning "未测试的操作系统: $OSTYPE"
    fi
    
    # 检查Python
    if command -v python3 &> /dev/null; then
        PYTHON_VERSION=$(python3 --version)
        print_info "Python: $PYTHON_VERSION"
    else
        print_error "Python3 未安装"
        exit 1
    fi
    
    # 检查pip
    if command -v pip3 &> /dev/null; then
        print_info "pip3 可用"
    else
        print_error "pip3 未安装"
        exit 1
    fi
}

# 安装系统依赖
install_system_deps() {
    print_info "安装系统依赖..."
    
    # 检查包管理器
    if command -v apt-get &> /dev/null; then
        print_info "使用apt-get安装FFmpeg..."
        sudo apt-get update
        sudo apt-get install -y ffmpeg
    elif command -v yum &> /dev/null; then
        print_info "使用yum安装FFmpeg..."
        sudo yum install -y ffmpeg
    elif command -v conda &> /dev/null; then
        print_info "使用conda安装FFmpeg..."
        conda install -y ffmpeg
    else
        print_warning "未找到包管理器，请手动安装FFmpeg"
    fi
    
    # 验证FFmpeg安装
    if command -v ffmpeg &> /dev/null; then
        FFMPEG_VERSION=$(ffmpeg -version | head -n1)
        print_info "FFmpeg: $FFMPEG_VERSION"
    else
        print_error "FFmpeg 安装失败"
        exit 1
    fi
}

# 安装Python依赖
install_python_deps() {
    print_info "安装Python依赖..."
    
    # 创建requirements.txt
    cat > requirements.txt << EOF
ultralytics>=8.0.0
opencv-python>=4.5.0
flask>=2.0.0
numpy>=1.21.0
torch>=1.9.0
torchvision>=0.10.0
EOF
    
    # 安装依赖
    pip3 install -r requirements.txt
    
    print_info "Python依赖安装完成"
}

# 检查模型文件
check_model() {
    print_info "检查模型文件..."
    
    MODEL_PATH="runs/detect/pig_detection/weights/best.pt"
    
    if [[ -f "$MODEL_PATH" ]]; then
        print_info "模型文件存在: $MODEL_PATH"
    else
        print_warning "模型文件不存在: $MODEL_PATH"
        print_info "将使用YOLO11x.pt预训练模型"
        
        # 下载预训练模型
        python3 -c "
from ultralytics import YOLO
model = YOLO('yolo11x.pt')
print('预训练模型下载完成')
"
    fi
}

# 测试连接
test_connection() {
    print_info "测试视频流连接..."
    
    if [[ -f "test_stream_connection.py" ]]; then
        python3 test_stream_connection.py
    else
        print_warning "测试脚本不存在，跳过连接测试"
    fi
}

# 配置服务
configure_service() {
    print_info "配置服务..."
    
    # 创建配置文件
    cat > config.json << EOF
{
    "model_path": "runs/detect/pig_detection/weights/best.pt",
    "stream_url": "wss://cameracc.cdelinks.cn:4431/rtp/34020000001320000065_34020000001320000065.live.flv",
    "hls_port": 8081,
    "confidence_threshold": 0.25,
    "iou_threshold": 0.45
}
EOF
    
    print_info "配置文件已创建: config.json"
}

# 启动服务
start_service() {
    print_info "准备启动服务..."
    
    echo ""
    echo "=========================================="
    echo "部署完成！"
    echo "=========================================="
    echo ""
    echo "启动服务命令："
    echo "  python3 start_pig_stream.py --port 8081"
    echo ""
    echo "或者："
    echo "  python3 predict_pig.py --mode stream --hls-port 8081"
    echo ""
    echo "访问方式："
    echo "  1. 在极值算力实例列表中点击'详情'"
    echo "  2. 找到'Web应用预览'并点击'查看'"
    echo "  3. 确保配置的端口为: 8081"
    echo ""
    echo "可用路径："
    echo "  /              - 状态页面"
    echo "  /live.m3u8     - HLS直播流"
    echo "  /stats         - 检测统计"
    echo ""
    
    read -p "是否立即启动服务？(y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        print_info "启动服务..."
        python3 start_pig_stream.py --port 8081
    else
        print_info "您可以稍后手动启动服务"
    fi
}

# 主函数
main() {
    check_root
    check_system
    install_system_deps
    install_python_deps
    check_model
    configure_service
    test_connection
    start_service
}

# 错误处理
trap 'print_error "部署过程中发生错误，请检查日志"; exit 1' ERR

# 运行主函数
main "$@"
