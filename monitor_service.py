#!/usr/bin/env python3
"""
猪检测推流服务监控脚本
实时监控服务状态、性能和检测结果
"""

import time
import requests
import json
import sys
import argparse
from datetime import datetime
import threading
import signal

class ServiceMonitor:
    def __init__(self, host='localhost', port=8081, interval=5):
        self.host = host
        self.port = port
        self.interval = interval
        self.base_url = f"http://{host}:{port}"
        self.running = True
        self.stats_history = []
        
    def check_service_health(self):
        """检查服务健康状态"""
        try:
            response = requests.get(f"{self.base_url}/", timeout=5)
            return response.status_code == 200
        except:
            return False
    
    def get_stats(self):
        """获取统计信息"""
        try:
            response = requests.get(f"{self.base_url}/stats", timeout=5)
            if response.status_code == 200:
                return response.json()
        except:
            pass
        return None
    
    def get_stream_info(self):
        """获取流信息"""
        try:
            response = requests.get(f"{self.base_url}/stream_url", timeout=5)
            if response.status_code == 200:
                return response.json()
        except:
            pass
        return None
    
    def check_hls_stream(self):
        """检查HLS流是否可用"""
        try:
            response = requests.get(f"{self.base_url}/live.m3u8", timeout=5)
            return response.status_code == 200
        except:
            return False
    
    def print_status(self, stats, stream_info, hls_available):
        """打印状态信息"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        print(f"\n{'='*60}")
        print(f"监控时间: {current_time}")
        print(f"{'='*60}")
        
        # 服务状态
        service_health = self.check_service_health()
        status_icon = "🟢" if service_health else "🔴"
        print(f"服务状态: {status_icon} {'运行中' if service_health else '离线'}")
        
        # HLS流状态
        hls_icon = "🟢" if hls_available else "🔴"
        print(f"HLS流状态: {hls_icon} {'可用' if hls_available else '不可用'}")
        
        # 统计信息
        if stats:
            print(f"\n📊 检测统计:")
            print(f"  总帧数: {stats.get('total_frames', 0):,}")
            print(f"  检测到的猪: {stats.get('detected_pigs', 0):,}")
            
            # 计算检测率
            total_frames = stats.get('total_frames', 0)
            detected_pigs = stats.get('detected_pigs', 0)
            if total_frames > 0:
                detection_rate = (detected_pigs / total_frames) * 100
                print(f"  检测率: {detection_rate:.2f}%")
            
            last_detection = stats.get('last_detection_time')
            if last_detection:
                print(f"  最后检测时间: {last_detection}")
        else:
            print(f"\n📊 检测统计: 无法获取")
        
        # 流信息
        if stream_info:
            print(f"\n🔗 流信息:")
            print(f"  端口: {stream_info.get('port', 'N/A')}")
            print(f"  状态: {stream_info.get('status', 'N/A')}")
        
        # 历史趋势
        if len(self.stats_history) > 1:
            self.print_trends()
    
    def print_trends(self):
        """打印趋势信息"""
        if len(self.stats_history) < 2:
            return
        
        current = self.stats_history[-1]
        previous = self.stats_history[-2]
        
        if current and previous:
            frame_diff = current.get('total_frames', 0) - previous.get('total_frames', 0)
            pig_diff = current.get('detected_pigs', 0) - previous.get('detected_pigs', 0)
            
            print(f"\n📈 趋势 (过去 {self.interval} 秒):")
            print(f"  新增帧数: {frame_diff}")
            print(f"  新检测猪数: {pig_diff}")
            
            if frame_diff > 0:
                fps = frame_diff / self.interval
                print(f"  处理速度: {fps:.1f} FPS")
    
    def save_stats(self, stats):
        """保存统计信息到历史记录"""
        if stats:
            stats['timestamp'] = datetime.now().isoformat()
            self.stats_history.append(stats)
            
            # 只保留最近100条记录
            if len(self.stats_history) > 100:
                self.stats_history.pop(0)
    
    def export_stats(self, filename=None):
        """导出统计数据"""
        if not filename:
            filename = f"stats_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(self.stats_history, f, indent=2, ensure_ascii=False)
            print(f"\n📁 统计数据已导出到: {filename}")
        except Exception as e:
            print(f"\n❌ 导出失败: {e}")
    
    def run(self):
        """运行监控"""
        print(f"开始监控服务: {self.base_url}")
        print(f"监控间隔: {self.interval} 秒")
        print("按 Ctrl+C 停止监控")
        
        try:
            while self.running:
                # 获取各种状态信息
                stats = self.get_stats()
                stream_info = self.get_stream_info()
                hls_available = self.check_hls_stream()
                
                # 保存统计信息
                self.save_stats(stats)
                
                # 打印状态
                self.print_status(stats, stream_info, hls_available)
                
                # 等待下次检查
                time.sleep(self.interval)
                
        except KeyboardInterrupt:
            print("\n\n监控已停止")
            self.stop()
    
    def stop(self):
        """停止监控"""
        self.running = False
        
        # 询问是否导出数据
        if self.stats_history:
            try:
                response = input("是否导出统计数据？(y/n): ").strip().lower()
                if response in ['y', 'yes']:
                    self.export_stats()
            except:
                pass

def signal_handler(signum, frame):
    """信号处理器"""
    print("\n收到停止信号，正在退出...")
    sys.exit(0)

def main():
    parser = argparse.ArgumentParser(description='猪检测推流服务监控')
    parser.add_argument('--host', type=str, default='localhost',
                       help='服务主机地址')
    parser.add_argument('--port', type=int, default=8081,
                       help='服务端口')
    parser.add_argument('--interval', type=int, default=5,
                       help='监控间隔（秒）')
    parser.add_argument('--export', type=str,
                       help='导出统计数据到指定文件')
    
    args = parser.parse_args()
    
    # 设置信号处理
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # 创建监控器
    monitor = ServiceMonitor(args.host, args.port, args.interval)
    
    # 如果指定了导出文件，先尝试连接并导出一次数据
    if args.export:
        stats = monitor.get_stats()
        if stats:
            monitor.save_stats(stats)
            monitor.export_stats(args.export)
        else:
            print("无法获取统计数据")
        return
    
    # 运行监控
    monitor.run()

if __name__ == "__main__":
    main()
