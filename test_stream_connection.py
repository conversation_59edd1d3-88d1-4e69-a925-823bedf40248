#!/usr/bin/env python3
"""
测试视频流连接脚本
用于验证WSS流是否可以正常访问
"""

import cv2
import time
import sys
import subprocess

def test_ffmpeg():
    """测试FFmpeg是否可用"""
    print("测试FFmpeg...")
    try:
        result = subprocess.run(['ffmpeg', '-version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print("✓ FFmpeg 可用")
            return True
        else:
            print("✗ FFmpeg 不可用")
            return False
    except Exception as e:
        print(f"✗ FFmpeg 测试失败: {e}")
        return False

def test_opencv():
    """测试OpenCV是否可用"""
    print("测试OpenCV...")
    try:
        import cv2
        print(f"✓ OpenCV 版本: {cv2.__version__}")
        return True
    except Exception as e:
        print(f"✗ OpenCV 不可用: {e}")
        return False

def test_stream_connection(stream_url, test_duration=30):
    """测试流连接"""
    print(f"测试流连接: {stream_url}")
    print(f"测试时长: {test_duration}秒")
    
    # 尝试不同的连接方式
    methods = [
        ("直接连接", lambda: cv2.VideoCapture(stream_url)),
        ("FFmpeg处理", lambda: cv2.VideoCapture(f"ffmpeg -i {stream_url} -f rawvideo -pix_fmt bgr24 -", cv2.CAP_FFMPEG))
    ]
    
    for method_name, method_func in methods:
        print(f"\n尝试方法: {method_name}")
        
        try:
            cap = method_func()
            
            if not cap.isOpened():
                print(f"✗ {method_name} - 无法打开流")
                continue
            
            print(f"✓ {method_name} - 流已打开")
            
            # 尝试读取几帧
            frame_count = 0
            start_time = time.time()
            success_count = 0
            
            while time.time() - start_time < test_duration:
                ret, frame = cap.read()
                frame_count += 1
                
                if ret:
                    success_count += 1
                    if frame_count % 30 == 0:  # 每30帧打印一次
                        print(f"已读取 {frame_count} 帧，成功 {success_count} 帧")
                else:
                    if frame_count % 30 == 0:
                        print(f"读取失败，总计 {frame_count} 次尝试，成功 {success_count} 次")
                
                time.sleep(0.1)  # 避免过快读取
            
            cap.release()
            
            success_rate = success_count / frame_count if frame_count > 0 else 0
            print(f"测试完成 - 成功率: {success_rate:.2%} ({success_count}/{frame_count})")
            
            if success_rate > 0.5:  # 成功率超过50%认为连接正常
                print(f"✓ {method_name} - 连接正常")
                return True
            else:
                print(f"✗ {method_name} - 连接不稳定")
                
        except Exception as e:
            print(f"✗ {method_name} - 连接失败: {e}")
    
    return False

def main():
    print("=" * 60)
    print("视频流连接测试")
    print("=" * 60)
    
    # 测试基础依赖
    if not test_ffmpeg():
        print("请先安装FFmpeg")
        return 1
    
    if not test_opencv():
        print("请先安装OpenCV")
        return 1
    
    # 测试流连接
    stream_url = "wss://cameracc.cdelinks.cn:4431/rtp/34020000001320000065_34020000001320000065.live.flv"
    
    print(f"\n开始测试流连接...")
    if test_stream_connection(stream_url, test_duration=30):
        print("\n✓ 流连接测试通过，可以启动推流服务")
        return 0
    else:
        print("\n✗ 流连接测试失败，请检查网络和配置")
        return 1

if __name__ == "__main__":
    try:
        sys.exit(main())
    except KeyboardInterrupt:
        print("\n测试被用户中断")
        sys.exit(0)
