#!/usr/bin/env python3
"""
猪检测推流服务启动脚本
专为远程GPU服务器环境优化
"""

import os
import sys
import subprocess
import argparse

def check_dependencies():
    """检查必要的依赖"""
    print("检查依赖...")
    
    # 检查ffmpeg
    try:
        result = subprocess.run(['ffmpeg', '-version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print("✓ FFmpeg 已安装")
        else:
            print("✗ FFmpeg 未正确安装")
            return False
    except (subprocess.TimeoutExpired, FileNotFoundError):
        print("✗ FFmpeg 未安装或不在PATH中")
        print("请安装FFmpeg: apt-get install ffmpeg 或 conda install ffmpeg")
        return False
    
    # 检查Python包
    required_packages = ['ultralytics', 'opencv-python', 'flask', 'numpy']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"✓ {package} 已安装")
        except ImportError:
            missing_packages.append(package)
            print(f"✗ {package} 未安装")
    
    if missing_packages:
        print(f"请安装缺失的包: pip install {' '.join(missing_packages)}")
        return False
    
    return True

def main():
    parser = argparse.ArgumentParser(description='猪检测推流服务启动脚本')
    parser.add_argument('--model', type=str, 
                       default='runs/detect/pig_detection/weights/best.pt',
                       help='模型路径')
    parser.add_argument('--port', type=int, default=8081,
                       help='Web服务端口 (2000-65000)')
    parser.add_argument('--conf', type=float, default=0.25,
                       help='置信度阈值')
    parser.add_argument('--check-deps', action='store_true',
                       help='只检查依赖，不启动服务')
    
    args = parser.parse_args()
    
    # 检查端口范围
    if not (2000 <= args.port <= 65000):
        print(f"错误: 端口 {args.port} 不在允许范围 2000-65000 内")
        return 1
    
    # 检查依赖
    if not check_dependencies():
        print("依赖检查失败，请安装缺失的依赖后重试")
        return 1
    
    if args.check_deps:
        print("依赖检查完成，所有依赖都已安装")
        return 0
    
    # 检查模型文件
    if not os.path.exists(args.model):
        print(f"错误: 模型文件 {args.model} 不存在")
        print("请先训练模型或提供正确的模型路径")
        return 1
    
    print("\n" + "="*60)
    print("启动猪检测推流服务")
    print("="*60)
    print(f"模型: {args.model}")
    print(f"端口: {args.port}")
    print(f"置信度阈值: {args.conf}")
    print(f"视频源: wss://cameracc.cdelinks.cn:4431/rtp/34020000001320000065_34020000001320000065.live.flv")
    print("="*60)
    
    # 构建命令
    cmd = [
        sys.executable, 'predict_pig.py',
        '--mode', 'stream',
        '--model', args.model,
        '--hls-port', str(args.port),
        '--conf', str(args.conf),
        '--source', 'wss://cameracc.cdelinks.cn:4431/rtp/34020000001320000065_34020000001320000065.live.flv'
    ]
    
    print(f"执行命令: {' '.join(cmd)}")
    print("\n按 Ctrl+C 停止服务\n")
    
    try:
        # 启动服务
        subprocess.run(cmd)
    except KeyboardInterrupt:
        print("\n服务已停止")
    except Exception as e:
        print(f"启动服务时出错: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
