# 猪检测推流服务 - 远程GPU服务器部署指南

本指南专门针对在远程GPU服务器（极值算力）上部署猪检测推流服务。

## 🚀 快速开始

### 1. 环境准备

确保您的远程GPU实例已安装以下依赖：

```bash
# 安装Python依赖
pip install ultralytics opencv-python flask numpy

# 安装FFmpeg（处理WebSocket流必需）
apt-get update && apt-get install -y ffmpeg
# 或使用conda
conda install ffmpeg
```

### 2. 配置Web应用预览端口

在创建极值算力实例时，请配置Web应用预览端口：
- 推荐端口：`8081`
- 端口范围：2000-65000
- 确保记住配置的端口号

### 3. 启动服务

#### 方法一：使用启动脚本（推荐）

```bash
# 检查依赖
python start_pig_stream.py --check-deps

# 启动服务（使用默认端口8081）
python start_pig_stream.py

# 自定义端口启动
python start_pig_stream.py --port 8081 --conf 0.3
```

#### 方法二：直接使用predict_pig.py

```bash
python predict_pig.py \
    --mode stream \
    --source wss://cameracc.cdelinks.cn:4431/rtp/34020000001320000065_34020000001320000065.live.flv \
    --hls-port 8081 \
    --conf 0.25
```

### 4. 访问服务

服务启动后，通过以下方式访问：

1. **在极值算力实例列表中**：
   - 点击实例的"详情"
   - 找到"Web应用预览"
   - 点击"查看"

2. **可用的URL路径**：
   - `/` - 服务状态页面
   - `/live.m3u8` - HLS直播流
   - `/stats` - 检测统计信息
   - `/stream_url` - 流URL信息

## 📋 功能特性

### 视频源配置
- **默认视频源**：`wss://cameracc.cdelinks.cn:4431/rtp/34020000001320000065_34020000001320000065.live.flv`
- **协议支持**：WebSocket Secure (WSS)
- **格式支持**：FLV流媒体
- **自动重连**：网络中断时自动重连

### 检测功能
- **模型**：YOLO11x预训练模型
- **检测对象**：猪
- **实时处理**：边检测边推流
- **可调参数**：置信度阈值、IOU阈值等

### 推流输出
- **格式**：HLS (HTTP Live Streaming)
- **分段时间**：2秒
- **播放列表大小**：5个片段
- **编码**：H.264

## 🔧 配置参数

### 启动脚本参数

```bash
python start_pig_stream.py [选项]

选项:
  --model PATH          模型路径 (默认: runs/detect/pig_detection/weights/best.pt)
  --port PORT           Web服务端口 (默认: 8081, 范围: 2000-65000)
  --conf FLOAT          置信度阈值 (默认: 0.25)
  --check-deps          只检查依赖，不启动服务
```

### predict_pig.py参数

```bash
python predict_pig.py [选项]

主要选项:
  --mode {image,video,stream}  运行模式
  --source SOURCE              输入源
  --hls-port PORT              HLS服务端口
  --conf FLOAT                 置信度阈值
  --model PATH                 模型路径
```

## 🛠️ 故障排查

### 常见问题

1. **无法连接视频源**
   ```
   错误: 经过 3 次尝试后仍无法打开输入源
   ```
   - 检查网络连接
   - 确认FFmpeg已正确安装
   - 验证视频流地址是否可访问

2. **Web应用预览无法访问**
   - 确认实例创建时已配置Web应用预览端口
   - 检查端口号是否与启动服务时使用的端口一致
   - 确认服务已成功启动

3. **推流中断**
   ```
   读取帧失败 (x/10)
   ```
   - 服务会自动尝试重连
   - 如果持续失败，检查网络稳定性

### 日志信息

服务启动时会显示详细的状态信息：
```
HLS服务器已启动: http://0.0.0.0:8081
直播流地址: http://0.0.0.0:8081/live.m3u8
统计信息: http://0.0.0.0:8081/stats
状态页面: http://0.0.0.0:8081/
==================================================
远程GPU服务器访问说明:
1. 确保在创建实例时配置了Web应用预览端口
2. 配置的端口应为: 8081
3. 通过平台的'Web应用预览'功能访问服务
==================================================
```

## 📊 监控和统计

访问 `/stats` 端点可获取实时统计信息：
```json
{
  "total_frames": 1500,
  "detected_pigs": 45,
  "last_detection_time": "2025-01-09T10:30:45"
}
```

## 🔄 服务管理

### 启动服务
```bash
python start_pig_stream.py --port 8081
```

### 停止服务
按 `Ctrl+C` 或关闭终端

### 重启服务
先停止服务，然后重新启动

## 📝 注意事项

1. **端口配置**：确保Web应用预览端口与服务启动端口一致
2. **网络稳定性**：WebSocket流对网络稳定性要求较高
3. **资源使用**：GPU推理 + 视频编码会消耗较多资源
4. **模型路径**：确保模型文件存在且可访问
5. **FFmpeg依赖**：处理WSS流必须安装FFmpeg

## 🆘 获取帮助

如果遇到问题，请检查：
1. 依赖是否完整安装
2. 端口配置是否正确
3. 网络连接是否稳定
4. 模型文件是否存在
